package com.io661.extension.service.Impl;

import com.google.gson.Gson;
import com.io661.extension.commonResult.CommonShowAlert;
import com.io661.extension.commonURL.CommonHttpUrl;
import com.io661.extension.model.Login.LoginReq;
import com.io661.extension.model.Login.SendCodeReq;
import com.io661.extension.model.Login.SendCodeRes;
import com.io661.extension.service.LoginService;
import javafx.scene.control.Alert;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;


public class LoginServiceImpl implements LoginService {
    private final CommonHttpUrl httpClient;

    public LoginServiceImpl() {
        this.httpClient = new CommonHttpUrl();
    }


    @Override
    // 登录方法
    public String login(String phone, String code) {
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            LoginReq request = new LoginReq();
            request.setPhone(phone);
            request.setCode(Integer.parseInt(code));
            request.setRemember(true);
            Gson gson = new Gson();
            String jsonBody = gson.toJson(request);

            String endpoint = "web/user";
            String response = httpClient.doPost(endpoint, jsonBody, headers);

            System.out.println("登录请求接口: " + endpoint);
            System.out.println("登录请求参数: " + jsonBody);
            System.out.println("服务器响应: " + response);

            return response;
        } catch (IOException e) {
            System.err.println("登录失败: " + e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return "";
    }

    @Override
    // 发送验证码
    public SendCodeRes sendCode(String phone) {
        SendCodeRes result = new SendCodeRes();


        try {
            String endpoint = "web/sms";

            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            SendCodeReq request = new SendCodeReq();
            request.setPhone(phone);
            request.setType(0);

            Gson gson = new Gson();
            String jsonBody = gson.toJson(request);

            System.out.println(jsonBody);

            String response = httpClient.doPost(endpoint, jsonBody, headers);

            SendCodeRes res = gson.fromJson(response, SendCodeRes.class);

            if (res.getCode() == 0) {
                result.setCode(0);
                result.setMsg(res.getMsg());
                return result;
            }
            result.setCode(res.getCode());
            result.setMsg(res.getMsg());
            return result;

        } catch (Exception e) {
            System.err.println("发送验证码失败: " + e.getMessage());
            return new SendCodeRes();
        }
    }
}

