package com.io661.extension.controller;

import com.google.gson.Gson;
import com.io661.extension.IO661Extension;
import com.io661.extension.commonResult.CommonResult;
import com.io661.extension.model.Login.LoginRes;
import com.io661.extension.model.Login.SendCodeRes;
import com.io661.extension.service.Impl.LoginServiceImpl;
import com.io661.extension.service.LoginService;
import com.io661.extension.util.User.UserCookieManager;
import javafx.animation.KeyFrame;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.TextField;
import javafx.util.Duration;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.awt.*;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ResourceBundle;

import static com.io661.extension.commonResult.CommonResult.webError;
import static com.io661.extension.commonResult.CommonResult.webSuccess;

@Data
public class LoginController implements Initializable {
    @Setter
    @Getter
    private static String authToken; // 存储授权令牌
    private LoginService loginService;
    private Timeline countdownTimer;
    private int countdownSeconds = 60;
    @FXML
    private CheckBox agreeCheckBox;
    @FXML
    private Button codeBtn;
    @FXML
    private TextField codeText;
    @FXML
    private Button loginBtn;
    @FXML
    private TextField phoneText;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // 初始化LoginService
        loginService = new LoginServiceImpl();

        // 设置手机号输入限制（只能输入11位数字）
        if (phoneText != null) {
            // 限制只能输入数字
            phoneText.textProperty().addListener((observable, oldValue, newValue) -> {
                if (newValue != null && !newValue.matches("\\d*")) {
                    phoneText.setText(newValue.replaceAll("[^\\d]", ""));
                }
                // 限制最多11位
                if (newValue != null && newValue.length() > 11) {
                    phoneText.setText(oldValue);
                }
            });
        }

        // 设置验证码输入限制（只能输入6位数字）
        if (codeText != null) {
            // 限制只能输入数字
            codeText.textProperty().addListener((observable, oldValue, newValue) -> {
                if (newValue != null && !newValue.matches("\\d*")) {
                    codeText.setText(newValue.replaceAll("[^\\d]", ""));
                }
                // 限制最多6位
                if (newValue != null && newValue.length() > 6) {
                    codeText.setText(oldValue);
                }
            });

            // 设置验证码输入框右边距，避免与按钮重叠
            codeText.setStyle("-fx-padding: 0 85px 0 5px;");
        }

        // 确保协议复选框可见
        if (agreeCheckBox != null) {
            agreeCheckBox.setVisible(true);
            agreeCheckBox.setManaged(true);
            // 默认不选中
            agreeCheckBox.setSelected(false);
        }
    }

    @FXML
    // 手机号输入框
    public String phoneTextOnAction(ActionEvent event) {
        Object mouseEvent = event.getSource();
        return mouseEvent.toString();
    }

    @FXML
    private void sendCodeBtnOnAction() {
        // 1. 禁用按钮防止重复点击
        codeBtn.setDisable(true);

        // 2. 启动倒计时
        if (countdownTimer != null) {
            countdownTimer.stop();
        }

        countdownTimer = new Timeline(new KeyFrame(
                Duration.seconds(1),
                event -> {
                    countdownSeconds--;
                    codeBtn.setText(countdownSeconds + "秒后重发");

                    // 确保倒计时期间按钮文字清晰可见
                    if (!codeBtn.getStyle().contains("-fx-font-weight: bold")) {
                        codeBtn.setStyle("-fx-font-weight: bold; -fx-text-fill: #999999;");
                    }

                    if (countdownSeconds <= 0) {
                        resetCountdown();
                    }
                }
        ));

        countdownTimer.setCycleCount(60);
        countdownTimer.play();

        // 3. 调用实际发送验证码的逻辑
        sendVerificationCode();
    }

    private void resetCountdown() {
        countdownTimer.stop();
        countdownSeconds = 60;
        codeBtn.setText("发送验证码");
        codeBtn.setDisable(false);
        // 恢复按钮默认样式
        codeBtn.setStyle("");
    }

    private void sendVerificationCode() {
        // 检查手机号是否为空
        String phone = phoneText.getText();
        if (phone == null || phone.trim().isEmpty()) {
            // 如果手机号为空，重置倒计时并显示提示
            resetCountdown();
            showAlert(Alert.AlertType.WARNING, "提示", "请输入手机号");
        }

        // 检查手机号格式是否正确（11位数字）
        if (phone != null && phone.length() != 11) {
            resetCountdown();
            showAlert(Alert.AlertType.WARNING, "提示", "请输入正确的手机号（11位）");
        }

        // 发送验证码
        SendCodeRes res = loginService.sendCode(phone);

        if (res.getCode() == 0) {
            showAlert(Alert.AlertType.INFORMATION, "成功", res.getMsg());
        } else {
            // 发送失败，重置倒计时
            resetCountdown();
            showAlert(Alert.AlertType.ERROR, "失败", res.getMsg());
        }
    }

    @FXML
        //  同意隐私协议
    void agreeCheckBoxOnAction(ActionEvent event) {

    }

    @FXML
    //  验证码输入框
    public String codeTextOnAction(ActionEvent event) {
        Object mouseEvent = event.getSource();
        return mouseEvent.toString();
    }
    @FXML
    //  登录
    public void loginBtnOnAction(ActionEvent event) {
        // 检查是否同意隐私协议
        if (!agreeCheckBox.isSelected()) {
            showAlert(Alert.AlertType.WARNING, "提示", "请同意服务协议和隐私协议");
            return;
        }

        // 检查手机号和验证码是否为空
        String phone = phoneText.getText();
        String code = codeText.getText();

        if (phone == null || phone.trim().isEmpty()) {
            showAlert(Alert.AlertType.WARNING, "提示", "请输入手机号");
            return;
        }

        if (code == null || code.trim().isEmpty()) {
            showAlert(Alert.AlertType.WARNING, "提示", "请输入验证码");
            return;
        }

        // 禁用登录按钮，防止重复点击
        loginBtn.setDisable(true);

        try {
            // 调用登录服务
            String response = loginService.login(phone, code);

            if (response != null && !response.isEmpty()) {
                // 保存授权令牌
                Gson gson = new Gson();
                LoginRes loginRes = gson.fromJson(response, LoginRes.class);

                if (loginRes != null && loginRes.getCode() == 0 && loginRes.getData() != null) {
                    String cookie = loginRes.getData().getAuthorization();
                    if (cookie != null && !cookie.isEmpty()) {
                        authToken = cookie;
                        System.out.println("登录成功，获取到授权令牌: " + cookie);

                        boolean saved = UserCookieManager.saveToken(cookie);
                        if (saved) {
                            System.out.println("授权令牌已保存到本地");
                        } else {
                            System.err.println("授权令牌保存到本地失败");
                        }

                        // 立即关闭登录窗口，然后显示成功提示
                        Platform.runLater(() -> {
                            // 先关闭登录窗口
                            IO661Extension.closeLoginStage();

                            // 获取主窗口控制器并更新UI
                            MainWindowController mainController = MainWindowController.getInstance();
                            if (mainController != null) {
                                mainController.updateAfterLogin();
                            }

                            // 最后显示成功提示（非阻塞）
                            Platform.runLater(() -> {
                                showAlert(Alert.AlertType.INFORMATION, "成功", "登录成功");
                            });
                        });
                    }
                }
            } else {
                showAlert(Alert.AlertType.ERROR, "错误", "登录失败，请检查手机号和验证码是否正确");
                loginBtn.setDisable(false);
            }
        } catch (Exception e) {
            System.err.println("登录过程发生异常: " + e.getMessage());
            showAlert(Alert.AlertType.ERROR, "错误", "登录过程发生异常: " + e.getMessage());
            loginBtn.setDisable(false);
        }
    }

    // 显示提示对话框
    private void showAlert(Alert.AlertType alertType, String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(alertType);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    @FXML
    private void openServiceAgreement() {
        openWebpage("https://io661.com/notice?id=1");
    }

    @FXML
    private void openPrivacyPolicy() {
        openWebpage("https://io661.com/notice?id=2");
    }

    private void openWebpage(String url) {
        try {
            Desktop.getDesktop().browse(new URI(url));
        } catch (IOException | URISyntaxException e) {
            System.out.println(e.getMessage());
        }
    }

}
